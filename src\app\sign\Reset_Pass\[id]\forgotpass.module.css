@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

.main {
  background-color: white;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
}

.leftContainer {
  background-color: #fff;
  width: 55%;
  height: 80%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media screen and (max-width: 900px) {
    width: 100%;
  }
}

.leftBody {
  color: black;
  justify-content: center;
  align-items: center;

}

.logo {
  font-size: 40px;
  font-weight: 700;
  font-family: poppins;

  @media screen and (max-width: 576px) {
    width: 75%;
    margin: auto;
    margin-top: 20px !important;
  }
}

.heading {
  margin: 10px 0;

  @media screen and (max-width: 576px) {
    width: 75%;
    margin: auto;
  }
}

.subHeading {
  margin: 10px 0;

  @media screen and (max-width: 576px) {
    width: 75%;
    margin: auto;
    margin-top: 10px !important;
    margin-bottom: 10px !important;
  }
}

.emailLink {
  font-weight: 600;
  margin-top: 20px;
}

/* google button */
.buttonContainer {
  width: 95%;
  margin: auto;
  height: 20px;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #cdcdcd;
  padding: 15px;
  background-color: #fffafa;

  @media screen and (max-width: 576px) {
    width: 70%;

    @media screen and (max-width: 576px) {
      width: 70%;
      justify-content: center;
    }
  }
}

.buttonContainer button {
  background-color: #fff;
  color: black;
  outline: none;
  border: none;
}

/* or PART */
.orContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.or {
  color: #cdcdcd;
  font-size: 16px;
  font-family: poppins;
}

.line1 {
  margin-bottom: 8px;
  margin-right: 5px;
}

.line2 {
  margin-bottom: 8px;
  margin-left: 5px;
}

/* formName */
.name {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
}

.name>label {
  margin-bottom: 10px;
  margin-left: 4px;
  font-weight: bold;
  font-family: poppins;
}

.name>input {
  background-color: transparent;
  height: 35px;
  border-radius: 8px;
  outline: none;
  border: 1px solid #ccc;
  color: black;
  padding: 10px;
}

/* formName */
/* email */

.emailContainer {
  margin-bottom: 5px;

  @media screen and (max-width: 576px) {
    width: 75%;
    margin: auto;
    margin-bottom: 5px !important;
  }
}

.email {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
}

.email>label {
  margin-bottom: 10px;
  margin-left: 4px;
  font-weight: bold;
  font-family: poppins;
}

.email>input {
  background-color: transparent;
  height: 35px;
  border-radius: 8px;
  outline: none;
  border: 1px solid #ccc;
  color: black;
  padding: 10px;
}

/* email */

/* password */

.passwordContainer {
  margin-top: 5px;
  position: relative;

  @media screen and (max-width: 576px) {
    width: 75%;
    margin: auto;
  }
}

.forgotpass {
  position: absolute;
  right: 0;
  top: 5px;
  font-size: 12px;
  font-family: poppins;
  color: blue;

}

.signUp {
  font-size: 14px;
  font-family: poppins;
  color: blue;
  font-weight: bold;
}

.password {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
  position: relative;
}

.password>label {
  margin-bottom: 10px;
  margin-left: 4px;
  font-weight: bold;
  font-family: poppins;
}

.password>input {
  background-color: transparent;
  height: 35px;
  border-radius: 8px;
  outline: none;
  border: 1px solid #ccc;
  color: black;
  padding: 10px;
  padding-right: 40px;
}

.hidePass {
  position: absolute;
  right: 10px;
  top: calc(50% + 5px);
  transform: translateY(-50%);
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.hidePass:hover {
  color: #333;
}

.wrongPassDialogue {
  font-size: 12px;
  color: #e74c3c;
  margin-top: 5px;
  margin-left: 4px;
  font-family: poppins;
  min-height: 16px;
  line-height: 1.2;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* password */

/* terms */

.terms {
  display: flex;
  margin-left: 3px;

  @media screen and (max-width: 576px) {
    width: 75%;
    margin: auto;
    margin-top: 10px !important;
  }
}

.termsDialogue {
  font-size: 12px;
  font-family: poppins;
  color: black;
  margin-left: 6px;
  margin-top: 2px;

  @media screen and (max-width: 576px) {
    width: 75%;
    margin: auto;
    margin-left: 0px !important;
  }
}

.loginBtnContainer {
  width: 100%;
  margin-top: 17px;
  margin-bottom: 60px;
  cursor: pointer;

  @media screen and (max-width: 576px) {
    width: 75%;
    margin: auto;
    margin-top: 17px !important;
    margin-bottom: 60px !important;
  }
}

.loginBtn {
  width: 100%;
  color: #4153ed;
  height: 42px;
  border-radius: 12px;
  border: 2px solid #4153ed;
  background: transparent;
  font-weight: 600;
  font-size: 16px;
  font-family: poppins;
  cursor: pointer;
  transition: all 0.3s ease;
}

.loginBtn:hover:not(:disabled) {
  background: #4153ed;
  color: white;
}

.loginBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f5f5;
  color: #999;
  border-color: #ddd;
}

/* terms */

/* lastPart */

.lastPart {
  font-size: 14px;
  font-family: poppins;
  margin-top: 25px;
  margin-left: 10px;

  @media screen and (max-width: 576px) {
    width: 75%;
    margin: auto;
    margin-top: 17px !important;
    margin-bottom: 60px !important;
  }
}

.baseLinksContainer {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  font-family: poppins;
  color: #000;
  /* font-family: Montserrat; */
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  text-decoration-line: underline;
}

.endBtns {
  display: flex;
  justify-content: flex-start;
  margin-top: 10px;
}

.fraudBtn {
  display: inline-flex;
  padding: 8px 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 5px;
  background: #EDEDED;
  color: #000;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  border: none;
  margin-right: 10px;
}


.reportBug {
  display: inline-flex;
  padding: 8px 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 5px;
  background: #FFF5F8;
  color: #F1416C;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  border: none;

}

/* lastPart */

/* LEFT CONTAINER  👆 */
/* RIGHT CONTAINER 👇 */
.rightContainer {
  width: 45%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  @media screen and (max-width: 900px) {
    width: 0%;
  }
}

.rightBody {
  width: 95%;
  height: 95%;
  border-radius: 10px;
  background-color: #4153ed;
  position: relative;
}

.textContainer {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  width: 50%;
  height: 100%;
  margin-left: 120px;


}

.bottomSection {
  display: flex;
  width: 70%;
  position: absolute;
  bottom: 20px;
  justify-content: space-between;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.btnCont {
  @media screen and (max-width: 576px) {
    display: none;
  }
}

.baseLinks {
  @media screen and (max-width: 576px) {
    display: none;
  }

}

.accessibilityBtn {
  display: inline-flex;
  padding: 12px 10px;
  justify-content: center;
  align-items: center;
  gap: 7px;
  border-radius: 5px;
  background: #FFF;
  color: #407BFF;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  border: none;
  margin-right: 10px;

  @media screen and (max-width: 576px) {
    display: none;
  }
}


.socialLinksCont {
  display: flex;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.firstLine {
  font-size: 16px;
  font-weight: 700;
  font-family: poppins;
  color: #fff;
  margin: 10px 0;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.secondLine {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  margin: 10px 0;
  padding-right: 50px;
  font-family: poppins;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.lastLine {
  font-size: 16px;
  font-weight: 180;
  color: #fff;
  font-family: poppins;
  margin: 10px 0;
  padding-right: 50px;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.toolTip {
  position: absolute;
  bottom: 0px;
  right: 0px;
  background-color: #407BFF;
}