"use client";
import { useState } from "react";
import { useParams } from "next/navigation";
import Image from "next/image";
import styles from "./forgotpass.module.css";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { setNewPassApi } from "@/app/api/onboarding/forgotPassword";
import socialImg from "../../../../../public/social.png";

require("dotenv").config();

const Reset_Pass = () => {
  const router = useRouter();
  const { id } = useParams();

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passDialogue, setPassDialogue] = useState("");
  const [confirmPassDialogue, setConfirmPassDialogue] = useState("");
  const [btnIsActive, setBtnIsActive] = useState(false);

  const handlePassword = (e) => {
    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;

    if (!passwordRegex.test(e.target.value)) {
      setPassDialogue(
        "Password must be 8-25 characters, with at least one uppercase, one lowercase, one digit, and one special character."
      );
    } else {
      setPassDialogue("");
    }
    setPassword(e.target.value);
  };

  const handleConfirmPassword = (e) => {
    const confirmPass = e.target.value;
    setConfirmPassword(confirmPass);

    if (password && confirmPass && password !== confirmPass) {
      setConfirmPassDialogue("Passwords do not match.");
    } else {
      setConfirmPassDialogue("");
    }
  };

  const BaseURL = process.env.NEXT_PUBLIC_RESETPASS_URL;

  const URL = `${BaseURL}/change-password/${id}`;

  const Data = {
    new_password: password,
    confirm_password: confirmPassword,
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    setPassDialogue("");
    setConfirmPassDialogue("");
    setBtnIsActive(true);

    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;

    if (!passwordRegex.test(password)) {
      setPassDialogue(
        "Password must be 8-25 characters, with at least one uppercase, one lowercase, one digit, and one special character."
      );
      setBtnIsActive(false);
      return;
    }

    if (password !== confirmPassword) {
      setConfirmPassDialogue("Passwords do not match.");
      setBtnIsActive(false);
      return;
    }

    try {
      const res = await setNewPassApi(URL, Data);

      if (res.status === 200) {
        toast.success(res.data.message);
        setTimeout(() => router.push("/sign/login"), 1500);
      } else {
        toast.error("Password reset failed.");
      }
    } catch (error) {
      console.log(error);
      toast.error(error.response?.data?.message || "An error occurred. Please try again.");
    } finally {
      setBtnIsActive(false);
    }
  };

  return (
    <main className={styles.main}>
      <div className={styles.leftContainer}>
        <div className={styles.leftBody}>
          <div className={styles.logo}>Remflow</div>
          {/* <div className={styles.logo}>Remflow new pass : </div> */}
          <h1 className={styles.heading}>Set New Password</h1>
          <div className={styles.subHeading}>
            Please enter your new Password and both should match.
          </div>

          <form action="" onSubmit={onSubmit}>
            <div className={styles.passwordContainer}>
              <div className={styles.password}>
                <label className={styles.nameLabel} htmlFor="password">
                  New password
                </label>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  maxLength={260}
                  onChange={handlePassword}
                  placeholder="Make sure it contains letters and numbers, at least 8 characters long."
                  required
                />
                <span
                  className={styles.hidePass}
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {!showPassword ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      width={20}
                      height={20}
                    >
                      <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 576 512"
                      width={20}
                      height={20}
                    >
                      <path d="M288 80c-65.2 0-118.8 29.6-159.9 67.7C89.6 183.5 63 226 49.4 256c13.6 30 40.2 72.5 78.6 108.3C169.2 402.4 222.8 432 288 432s118.8-29.6 159.9-67.7C486.4 328.5 513 286 526.6 256c-13.6-30-40.2-72.5-78.6-108.3C406.8 109.6 353.2 80 288 80zM95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 336c44.2 0 80-35.8 80-80s-35.8-80-80-80c-.7 0-1.3 0-2 0c1.3 5.1 2 10.5 2 16c0 35.3-28.7 64-64 64c-5.5 0-10.9-.7-16-2c0 .7 0 1.3 0 2c0 44.2 35.8 80 80 80zm0-208a128 128 0 1 1 0 256 128 128 0 1 1 0-256z" />
                    </svg>
                  )}
                </span>
              </div>
              <div className={styles.wrongPassDialogue}>
                {passDialogue || "\u00A0"}
              </div>
              <div className={styles.password}>
                <label className={styles.nameLabel} htmlFor="confirm_password">
                  Confirm password
                </label>
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  maxLength={260}
                  id="confirm_password"
                  value={confirmPassword}
                  onChange={handleConfirmPassword}
                  placeholder="Passwords should match"
                  required
                />
                <span
                  className={styles.hidePass}
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {!showConfirmPassword ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      width={20}
                      height={20}
                    >
                      <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 576 512"
                      width={20}
                      height={20}
                    >
                      <path d="M288 80c-65.2 0-118.8 29.6-159.9 67.7C89.6 183.5 63 226 49.4 256c13.6 30 40.2 72.5 78.6 108.3C169.2 402.4 222.8 432 288 432s118.8-29.6 159.9-67.7C486.4 328.5 513 286 526.6 256c-13.6-30-40.2-72.5-78.6-108.3C406.8 109.6 353.2 80 288 80zM95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 336c44.2 0 80-35.8 80-80s-35.8-80-80-80c-.7 0-1.3 0-2 0c1.3 5.1 2 10.5 2 16c0 35.3-28.7 64-64 64c-5.5 0-10.9-.7-16-2c0 .7 0 1.3 0 2c0 44.2 35.8 80 80 80zm0-208a128 128 0 1 1 0 256 128 128 0 1 1 0-256z" />
                    </svg>
                  )}
                </span>
              </div>
              <div className={styles.wrongPassDialogue}>
                {confirmPassDialogue || "\u00A0"}
              </div>
            </div>
            <div className={styles.loginBtnContainer}>
              <button
                type="submit"
                className={styles.loginBtn}
                disabled={btnIsActive}
              >
                Reset Password
              </button>
            </div>
          </form>
          <ToastContainer />
        </div>
      </div>
      <div className={styles.rightContainer}>
        <div className={styles.rightBody}>
          <div className={styles.textContainer}>
            <div>
              <div className={styles.firstLine}>
                Please login to access the platform
              </div>
              <div className={styles.secondLine}>
                In order to comply with regulatory standards and maintain the
                integrity of our platform, we respectfully ask our users to
                provide accurate personal information for identity verification.
                This process usually takes just a few minutes to complete.
              </div>
              <div className={styles.lastLine}>
                It helps authenticate everyone involved in transactions,
                creating a secure and trusted trading environment.
              </div>
            </div>
            {/* bottom section */}
            <div className={styles.bottomSection}>
              <div className={styles.btnCont}>
                <button className={styles.accessibilityBtn}>
                  Accesability
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    viewBox="0 0 12 12"
                    fill="none"
                  >
                    <path
                      d="M6 11.25C4.60761 11.25 3.27226 10.6969 2.28769 9.71231C1.30312 8.72775 0.75 7.39239 0.75 6C0.75 4.60761 1.30312 3.27226 2.28769 2.28769C3.27226 1.30312 4.60761 0.75 6 0.75C7.39239 0.75 8.72775 1.30312 9.71231 2.28769C10.6969 3.27226 11.25 4.60761 11.25 6C11.25 7.39239 10.6969 8.72775 9.71231 9.71231C8.72775 10.6969 7.39239 11.25 6 11.25ZM6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12Z"
                      fill="#407BFF"
                    />
                    <path
                      d="M6.944 5.03739L5.11196 5.22674L5.04636 5.47746L5.40637 5.53222C5.64157 5.5784 5.68797 5.64834 5.63677 5.84165L5.04636 8.12976C4.89116 8.72157 5.13036 9 5.69278 9C6.12878 9 6.63519 8.83374 6.8648 8.60545L6.9352 8.33099C6.7752 8.44711 6.54159 8.49329 6.38639 8.49329C6.16638 8.49329 6.08638 8.36596 6.14318 8.14163L6.944 5.03739ZM7 3.65978C7 3.83476 6.91571 4.00258 6.76568 4.12631C6.61565 4.25004 6.41216 4.31955 6.19998 4.31955C5.98781 4.31955 5.78432 4.25004 5.63429 4.12631C5.48426 4.00258 5.39997 3.83476 5.39997 3.65978C5.39997 3.48479 5.48426 3.31698 5.63429 3.19324C5.78432 3.06951 5.98781 3 6.19998 3C6.41216 3 6.61565 3.06951 6.76568 3.19324C6.91571 3.31698 7 3.48479 7 3.65978Z"
                      fill="#407BFF"
                    />
                  </svg>
                </button>
                <button className={styles.accessibilityBtn}>
                  Regulatory authority info
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="9"
                    height="11"
                    viewBox="0 0 9 11"
                    fill="none"
                  >
                    <path
                      d="M8.02083 8.625V9.875H0.729167V8.625H0V9.875C0 10.0408 0.0768227 10.1997 0.213568 10.3169C0.350313 10.4342 0.53578 10.5 0.729167 10.5H8.02083C8.21422 10.5 8.39969 10.4342 8.53643 10.3169C8.67318 10.1997 8.75 10.0408 8.75 9.875V8.625H8.02083Z"
                      fill="#407BFF"
                    />
                    <path
                      d="M8.125 4.93182L7.59625 4.41108L4.75 7.21051V0.5H4V7.21051L1.15375 4.41108L0.625 4.93182L4.375 8.625L8.125 4.93182Z"
                      fill="#407BFF"
                    />
                  </svg>
                </button>
              </div>
              <div className={styles.socialLinksCont}>
                <Image src={socialImg} alt="Picture of the author" />
              </div>
            </div>
            {/* bottom section */}

            {/* mobile tooltip */}

            {/* mobile tooltip */}
          </div>
        </div>
      </div>

      {/* <div className={styles.toolTip}>
        {" "}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
        >
          <path
            d="M6 11.25C4.60761 11.25 3.27226 10.6969 2.28769 9.71231C1.30312 8.72775 0.75 7.39239 0.75 6C0.75 4.60761 1.30312 3.27226 2.28769 2.28769C3.27226 1.30312 4.60761 0.75 6 0.75C7.39239 0.75 8.72775 1.30312 9.71231 2.28769C10.6969 3.27226 11.25 4.60761 11.25 6C11.25 7.39239 10.6969 8.72775 9.71231 9.71231C8.72775 10.6969 7.39239 11.25 6 11.25ZM6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12Z"
            fill="#407BFF"
          />
          <path
            d="M6.944 5.03739L5.11196 5.22674L5.04636 5.47746L5.40637 5.53222C5.64157 5.5784 5.68797 5.64834 5.63677 5.84165L5.04636 8.12976C4.89116 8.72157 5.13036 9 5.69278 9C6.12878 9 6.63519 8.83374 6.8648 8.60545L6.9352 8.33099C6.7752 8.44711 6.54159 8.49329 6.38639 8.49329C6.16638 8.49329 6.08638 8.36596 6.14318 8.14163L6.944 5.03739ZM7 3.65978C7 3.83476 6.91571 4.00258 6.76568 4.12631C6.61565 4.25004 6.41216 4.31955 6.19998 4.31955C5.98781 4.31955 5.78432 4.25004 5.63429 4.12631C5.48426 4.00258 5.39997 3.83476 5.39997 3.65978C5.39997 3.48479 5.48426 3.31698 5.63429 3.19324C5.78432 3.06951 5.98781 3 6.19998 3C6.41216 3 6.61565 3.06951 6.76568 3.19324C6.91571 3.31698 7 3.48479 7 3.65978Z"
            fill="#407BFF"
          />
        </svg>
      </div> */}
    </main>
  );
};

export default Reset_Pass;
